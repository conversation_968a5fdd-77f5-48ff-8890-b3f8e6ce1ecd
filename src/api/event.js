import { getAction, postAction } from '@/api/manage'

// 事件来源
export const getEventSourceApi = () =>
  getAction('/event/eventCategory/getChildListBatch', {
    parentIds: 0
  })

// 事件分类
export const getEventTypeApi = parentIds =>
  getAction('/event/eventCategory/getChildListBatch', {
    parentIds
  })

// 事件办理
export const dealEventApi = data => postAction('/event/accept/handleEvent', data)
// 事件不予办理
export const notDealEventApi = data => postAction('/event/accept/unhandleEvent', data)

// 事件并案
export const mergeEventApi = data => postAction('/event/accept/mergeEvent', data)

// 查询当前用户的行政区划树
// export const getCurrentRegionApi = params => getAction('/workRegion/queryTreeById', params)

// 查询当前用户的行政区划树
export const getGridOptionsApi = regionCode =>
  getAction('/workGridDefined/queryPageListByRegionCode', {
    regionCode,
    pageSize: 100
  })
