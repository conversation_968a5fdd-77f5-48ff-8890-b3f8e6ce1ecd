import { getAction, postAction } from '@/api/manage'

// 工单类型树
export const queryEventItemByParentApi = pcode =>
  getAction('/right/queryEventItemByParent', {
    pcode
  })

// 获取带事项的事项树
export const queryEventItemEventsTreeApi = (name = undefined) =>
  getAction('/right/queryEventItemEventsTree', {
    name
  })

// 查询工单标签
export const getWorkLabelsApi = labelName =>
  getAction('/work/workLabel/list', {
    pageSize: 100,
    labelName
  })

// 获取相似工单列表
export const getSimilarOrderApi = ids => postAction('/work/workInfo/similarOrder', ids)

// 并案工单查询
export const getMergeListApi = params => getAction('/work/workInfo/mergeList', params)

// 通过工单id查询工单信息
export const getWorkOrderInfoByIdApi = id => getAction('/work/workInfo/queryById', { id })

export const getWorkOrderInfoByWorkNoApi = workNo => getAction('/work/workInfo/queryByWorkNo', { workNo })

// 获取权责清单推荐列表
export const getEventRecommendApi = workType => getAction('/work/workInfo/eventRecommend', { workType })

// 获取转办单位列表
export const getTransferUnitListApi = () => getAction('/config/listSysDepart')

// 工单分拨-保存
export const saveWorkAssignApi = data => postAction('/work/workAssign/saveWorkAssign', data)

// 工单分拨-转业务专家组
export const forwardExpertApi = workId => getAction('/work/workInfo/forwardExpert', { workId })

// 工单分拨-上报
export const reportApi = data => postAction('/work/workInfo/report', data)

// 获取上报部门
export const getReportListApi = () => getAction('/work/workInfo/getReportList')

// 工单分拨-下派
export const distributionApi = data => postAction('/work/workInfo/distribution', data)

// 获取下派部门
export const getDistributionListApi = () => getAction('/work/workInfo/getDistributionList')

// 工单分拨-作废
export const discardApi = data => postAction('/work/workInfo/discard', data)

// 工单签收
export const signWorkOrderApi = workId =>
  postAction('/work/workAssignDetail/sign', {
    id: workId,
    acccpteStatus: '1'
  })

// 工单拒签
export const rejectSignWorkOrderApi = (workId, msg) =>
  postAction('/work/workAssignDetail/sign', {
    id: workId,
    acccpteStatus: '2',
    acccpteMsg: msg
  })

// 工单处置
export const disposeWorkOrderApi = data => postAction('/work/workAssignDetail/dispose', data)

// 工单挂单申请
export const pendingWorkOrderApi = data => postAction('/work/workAssignDetail/pending', data)

// 工单解除挂单
export const cancelPendingApi = data => postAction('/work/workAssignDetail/pending/cancel', data)

// 工单延期申请
export const delayWorkOrderApi = data => postAction('/work/workAssignDetail/pending', data)

// 工单领导批示
export const approveWorkOrderApi = data => postAction('/work/workAssignDetail/pending', data)

// 获取审批人
export const getApproverApi = roleCode => getAction('/work/workAssignDetail/queryApproverByRoleCode', { roleCode })

// 获取工单处置流程记录
export const getWorkOrderProcessApi = workNo => getAction('/work/workAssignDetail/listByWorkNo', { workNo })

// 工单催办
export const urgeWorkOrderApi = ids => getAction('/work/workAssignDetail/urge', { ids })

// 领导审批通过
export const leadApprovalPassApi = (id, msg) =>
  postAction('/work/workApproval/leadApproval', {
    approvalStatus: '1',
    approvalMsg: msg,
    id
  })

// 领导驳回
export const leadApprovalRejectApi = (id, msg) =>
  postAction('/work/workApproval/leadApproval', {
    approvalStatus: '2',
    approvalMsg: msg,
    id
  })
