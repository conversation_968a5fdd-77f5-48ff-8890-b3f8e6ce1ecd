import request from '@/utils/request'
import da from 'element-ui/src/locale/lang/da'
import { deleteAction } from '@/api/manage'
import { getAction } from '@/api/manage'
import { postAction, putAction } from '@/api/manage'
// 我的发起的流程
export function myProcessList(query) {
  // return request({
  //   url: '/flowable/task/myProcess',
  //   method: 'get',
  //   params: query
  // })
  return getAction('/flowable/task/myProcess', query)
}

export function flowFormData(query) {
  // return request({
  //   url: '/flowable/task/flowFormData',
  //   method: 'get',
  //   params: query
  // })
  return getAction('/flowable/task/flowFormData', query)
}

// 完成任务
export function complete(data) {
  // return request({
  //   url: '/flowable/task/complete',
  //   method: 'post',
  //   data: data
  // })
  return postAction('/flowable/task/complete', data)
}

// 取消申请
export function stopProcess(data) {
  // return request({
  //   url: '/flowable/task/stopProcess',
  //   method: 'post',
  //   data: data
  // })
  return postAction('/flowable/task/stopProcess', data)
}

// 驳回任务
export function rejectTask(data) {
  // return request({
  //   url: '/flowable/task/reject',
  //   method: 'post',
  //   data: data
  // })
  return postAction('/flowable/task/reject', data)
}

// 可退回任务列表
export function returnList(data) {
  // return request({
  //   url: '/flowable/task/returnList',
  //   method: 'post',
  //   data: data
  // })
  return postAction('/flowable/task/returnList', data)
}

// 部署流程实例
export function deployStart(deployId) {
  // return request({
  //   url: '/flowable/process/startFlow/' + deployId,
  //   method: 'get',
  // })
  return getAction(`/flowable/process/startFlow/${deployId}`)
}

// 查询流程定义详细
export function getDeployment(id) {
  // return request({
  //   url: '/system/deployment/' + id,
  //   method: 'get'
  // })
  return getAction(`/system/deployment/${id}`)
}

// 新增流程定义
export function addDeployment(data) {
  // return request({
  //   url: '/system/deployment',
  //   method: 'post',
  //   data: data
  // })
  return postAction('/system/deployment', data)
}

// 修改流程定义
export function updateDeployment(data) {
  // return request({
  //   url: '/system/deployment',
  //   method: 'put',
  //   data: data
  // })
  return putAction('/system/deployment', data)
}

// 删除流程定义
export function delDeployment(id) {
  // return request({
  //   url: '/system/deployment/' + id,
  //   method: 'delete'
  // })
  deleteAction(`/system/deployment/${id}`)
}

// 导出流程定义
export function exportDeployment(query) {
  // return request({
  //   url: '/system/deployment/export',
  //   method: 'get',
  //   params: query
  // })
  return getAction('/system/deployment/export', query)
}
