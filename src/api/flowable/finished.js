import request from '@/utils/request'
import da from 'element-ui/src/locale/lang/da'
import { deleteAction } from '@/api/manage'
import { getAction } from '@/api/manage'
import { postAction, putAction } from '@/api/manage'
// 查询已办任务列表
export function finishedList(query) {
  // return request({
  //   url: '/flowable/task/finishedList',
  //   method: 'get',
  //   params: query
  // })
  return getAction('/flowable/task/finishedList', query)
}

// 任务流转记录
export function flowRecord(query) {
  // return request({
  //   url: '/flowable/task/flowRecord',
  //   method: 'get',
  //   params: query
  // })
  return postAction('/flowable/task/queryEventFlow', query)
}

// 撤回任务
export function revokeProcess(data) {
  // return request({
  //   url: '/flowable/task/revokeProcess',
  //   method: 'post',
  //   data: data
  // })
  return postAction('/flowable/task/revokeProcess', data)
}

// 部署流程实例
export function deployStart(deployId) {
  // return request({
  //   url: '/flowable/process/startFlow/' + deployId,
  //   method: 'get'
  // })
  return getAction(`/flowable/process/startFlow/${deployId}`)
}

// 查询流程定义详细
export function getDeployment(id) {
  // return request({
  //   url: '/system/deployment/' + id,
  //   method: 'get'
  // })
  return getAction(`/system/deployment/${id}`)
}

// 新增流程定义
export function addDeployment(data) {
  // return request({
  //   url: '/system/deployment',
  //   method: 'post',
  //   data: data
  // })
  return postAction('/system/deployment', data)
}

// 修改流程定义
export function updateDeployment(data) {
  // return request({
  //   url: '/system/deployment',
  //   method: 'put',
  //   data: data
  // })
  return putAction('/system/deployment', data)
}

// 删除流程定义
export function delDeployment(id) {
  // return request({
  //   url: '/flowable/instance/delete/' + id,
  //   method: 'delete'
  // })
  deleteAction(`/flowable/instance/delete/${id}`)
}

// 导出流程定义
export function exportDeployment(query) {
  // return request({
  //   url: '/system/deployment/export',
  //   method: 'get',
  //   params: query
  // })
  return getAction('/system/deployment/export', query)
}
