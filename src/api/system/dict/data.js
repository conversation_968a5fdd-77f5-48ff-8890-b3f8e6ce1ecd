import request from '@/utils/request'
import { deleteAction } from '@/api/manage'
import { getAction } from '@/api/manage'
import { postAction, putAction } from '@/api/manage'
// 查询字典数据列表
export function listData(query) {
  // return request({
  //   url: '/system/dict/data/list',
  //   method: 'get',
  //   params: query
  // })
  return getAction('/system/dict/data/list', query)
}

// 查询字典数据详细
export function getData(dictCode) {
  // return request({
  //   url: '/system/dict/data/' + dictCode,
  //   method: 'get'
  // })
  return getAction(`/system/dict/data/${dictCode}`)
}

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  // return request({
  //   url: '/system/dict/data/type/' + dictType,
  //   method: 'get'
  // })
  return getAction(`/system/dict/data/type/${dictType}`)
}

// 新增字典数据
export function addData(data) {
  // return request({
  //   url: '/system/dict/data',
  //   method: 'post',
  //   data: data
  // })
  return postAction('/system/dict/data', data)
}

// 修改字典数据
export function updateData(data) {
  // return request({
  //   url: '/system/dict/data',
  //   method: 'put',
  //   data: data
  // })
  return putAction('/system/dict/data', data)
}

// 删除字典数据
export function delData(dictCode) {
  // return request({
  //   url: '/system/dict/data/' + dictCode,
  //   method: 'delete'
  // })
  deleteAction(`/system/dict/data/${dictCode}`)
}
