import request from '@/utils/request'
import { deleteAction } from '@/api/manage'
import { getAction } from '@/api/manage'
import { postAction, putAction } from '@/api/manage'
// 查询流程达式列表
export function listExpression(query) {
  // return request({
  //   url: '/system/expression/list',
  //   method: 'get',
  //   params: query
  // })
  return getAction('/flowable/expression/list', query)
}

// 查询流程达式详细
export function getExpression(id) {
  // return request({
  //   url: '/system/expression/' + id,
  //   method: 'get'
  // })
  return getAction(`/system/expression/${id}`)
}

// 新增流程达式
export function addExpression(data) {
  // return request({
  //   url: '/system/expression',
  //   method: 'post',
  //   data: data
  // })
  return postAction('/system/expression', data)
}

// 修改流程达式
export function updateExpression(data) {
  // return request({
  //   url: '/system/expression',
  //   method: 'put',
  //   data: data
  // })
  return putAction('/system/expression', data)
}

// 删除流程达式
export function delExpression(id) {
  // return request({
  //   url: '/system/expression/' + id,
  //   method: 'delete'
  // })
  deleteAction(`/system/expression/${id}`)
}
