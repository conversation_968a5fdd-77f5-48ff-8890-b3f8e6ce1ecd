// 改
import executionListenerDialog from '../components/nodePanel/property/executionListener'
export default {
  components: {
    executionListenerDialog
  },
  data() {
    return {
      executionListenerLength: 0,
      dialogName: null
    }
  },
  methods: {
    computedExecutionListenerLength() {
      // this.executionListenerLength = this.element.businessObject.extensionElements?.values?.length ?? 0
      this.executionListenerLength = this.element.businessObject.extensionElements && this.element.businessObject.extensionElements.value ? this.element.businessObject.extensionElements.value.length : 0
      // if (this.element.businessObject.extensionElements) {
      //   if (this.element.businessObject.extensionElements.values) {
      //     this.executionListenerLength = this.element.businessObject.extensionElements.values.length
      //   } else {
      //     this.executionListenerLength = 0
      //   }
      // } else {
      //   this.executionListenerLength = 0
      // }
    },
    finishExecutionListener() {
      if (this.dialogName === 'executionListenerDialog') {
        this.computedExecutionListenerLength()
      }
      this.dialogName = ''
    }
  }
}
