// 改
export function commonParse(element) {
  const result = {
    ...element.businessObject,
    ...element.businessObject.$attrs
  }
  return formatJsonKeyValue(result)
}

export function formatJsonKeyValue(result) {
  // 移除flowable前缀，格式化数组
  for (const key in result) {
    if (key.indexOf('flowable:') === 0) {
      const newKey = key.replace('flowable:', '')
      result[newKey] = result[key]
      delete result[key]
    }
  }
  result = documentationParse(result)
  return result
}

export function documentationParse(obj) {
  if ('documentation' in obj) {
    let str = ''
    obj.documentation.forEach(item => {
      str += item.text
    })
    obj.documentation = str
  }
  return obj
}

export function conditionExpressionParse(obj) {
  if ('conditionExpression' in obj) {
    console.log('obj.conditionExpression-------------------------------', obj.conditionExpression)
    if (obj.conditionExpression != undefined && obj.conditionExpression != null) {
      var strTemp = obj.conditionExpression.body

      if (strTemp) {
        var startIndex1 = strTemp.indexOf("('")
        var startIndex2 = strTemp.indexOf('(')
        var middleIndex = strTemp.indexOf(',')
        var endIndex = strTemp.indexOf(')')

        if (middleIndex != -1) {
          obj.conditionExpression = strTemp.substring(0, middleIndex) + '#@#{}' + strTemp.substring(endIndex)
          obj.conditionValue = strTemp.substring(middleIndex + 1, endIndex)

          if (startIndex1 != -1) {
            obj.conditionVariable = obj.conditionExpression.substring(startIndex1 + 2, middleIndex - 1)
            obj.conditionExpression = obj.conditionExpression.substring(0, startIndex1) + "('#@#{myVar}'" + obj.conditionExpression.substring(middleIndex)
          } else {
            obj.conditionVariable = obj.conditionExpression.substring(startIndex2 + 1, middleIndex)
            obj.conditionExpression = obj.conditionExpression.substring(0, startIndex2) + '(#@#{myVar}' + obj.conditionExpression.substring(middleIndex)
          }
        } else {
          obj.conditionExpression = strTemp.replace(')}', '#@#{})}')
          obj.conditionValue = ''

          if (startIndex1 != -1) {
            obj.conditionVariable = obj.conditionExpression.substring(startIndex1 + 2, endIndex - 1)
            obj.conditionExpression = obj.conditionExpression.substring(0, startIndex1) + "('#@#{myVar}'" + obj.conditionExpression.substring(endIndex)
          } else {
            obj.conditionVariable = obj.conditionExpression.substring(startIndex2 + 1, endIndex)
            obj.conditionExpression = obj.conditionExpression.substring(0, startIndex2) + '(#@#{myVar}' + obj.conditionExpression.substring(endIndex)
          }
        }
      } else {
        obj.conditionVariable = ''
        obj.conditionExpression = ''
      }

      // obj.conditionExpression = obj.conditionExpression.substring(0, middleIndex)
    }
  }
  return obj
}

export function userTaskParse(obj) {
  for (const key in obj) {
    if (key === 'candidateUsers') {
      obj.userType = 'candidateUsers'
      // obj[key] = obj[key]?.split(',') || []
      obj[key] = obj[key] && obj[key].split(',') || []
    } else if (key === 'candidateGroups') {
      obj.userType = 'candidateDepts'
      // obj[key] = obj[key]?.split(',') || []
      obj[key] = obj[key] && obj[key].split(',') || []
    } else if (key === 'candidateDepts') {
      obj.userType = 'candidateDepts'
      // obj[key] = obj[key]?.split(',') || []
      obj[key] = obj[key] && obj[key].split(',') || []
    } else if (key === 'assignee') {
      obj.userType = 'assignee'
    }
  }
  return obj
}
