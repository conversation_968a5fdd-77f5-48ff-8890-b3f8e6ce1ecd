<!-- 改 -->
<template>
  <div>
    <x-form ref="xForm" v-model="formData" :config="formConfig">
      <template #executionListener>
        <el-badge :value="executionListenerLength">
          <el-button size="small" @click="dialogName = 'executionListenerDialog'">编辑</el-button>
        </el-badge>
      </template>

    </x-form>
    <executionListenerDialog
      v-if="dialogName === 'executionListenerDialog'"
      :element="element"
      :modeler="modeler"
      @close="finishExecutionListener"
    />
  </div>
</template>

<script>
import mixinPanel from '../../common/mixinPanel'
import { StrUtil } from '@/utils/StrUtil'
import mixinExecutionListener from '../../common/mixinExecutionListener'
import { commonParse, conditionExpressionParse } from '../../common/parseElement'
export default {
  mixins: [mixinPanel, mixinExecutionListener],
  data() {
    return {
      notValues: [3, 6, 7],
      forwardConditions: [
        {
          id: '1',
          value: '${var:contains(#@#{myVar}#@#{})}',
          label: '指定变量值列表'
        },
        {
          id: '2',
          value: '${var:containsAny(#@#{myVar}#@#{})}',
          label: '变量值在列表中'
        },
        {
          id: '3',
          value: '${var:eq(#@#{myVar}#@#{})}',
          label: '指定变量值'
        },
        {
          id: '4',
          value: '${var:exists(#@#{myVar}#@#{})}',
          label: '变量已存在'
        },
        {
          id: '5',
          value: "${var:gt('#@#{myVar}'#@#{})}",
          label: '变量值大于'
        },
        {
          id: '6',
          value: "${var:gte('#@#{myVar}'#@#{})}",
          label: '变量值大于等于'
        },
        {
          id: '7',
          value: '${variables:empty(#@#{myVar}#@#{})}',
          label: '变量值为空'
        },
        {
          id: '8',
          value: '${var:isNotEmpty(#@#{myVar}#@#{})}',
          label: '变量值不为空'
        },
        {
          id: '9',
          value: "${var:lt('#@#{myVar}'#@#{})}",
          label: '变量值小于'
        },
        {
          id: '10',
          value: '${var:lte(#@#{myVar}#@#{})}',
          label: '变量值小于等于'
        },
        {
          id: '11',
          value: '${variables:notEquals(#@#{myVar}#@#{})}',
          label: '变量值不等于'
        }
      ],
      formData: {},
      executionListenerLength: 0
    }
  },
  computed: {
    formConfig() {
      const _this = this
      return {
        inline: false,
        item: [
          {
            xType: 'input',
            name: 'id',
            label: '节点 id',
            rules: [{ required: true, message: 'Id 不能为空' }]
          },
          {
            xType: 'input',
            name: 'name',
            label: '节点名称'
          },
          {
            xType: 'input',
            name: 'documentation',
            label: '节点描述'
          },
          // {
          //   xType: 'slot',
          //   name: 'executionListener',
          //   label: '执行监听器'
          // },
          // {
          //   xType: 'input',
          //   name: 'conditionExpression',
          //   label: '跳转条件'
          // },
          {
            xType: 'select',
            name: 'conditionExpression',
            label: '条件表达式',
            // clearable: true,
            dic: _this.forwardConditions
          },
          {
            xType: 'input',
            name: 'conditionVariable',
            label: '条件变量'
          },
          {
            xType: 'input',
            name: 'conditionValue',
            label: '条件值',
            show: _this.notValues.indexOf(_this.forwardConditions.findIndex(item => _this.formData.conditionExpression == item.value)) == -1 && _this.formData.conditionExpression != null && _this.formData.conditionExpression != ''

          },
          {
            xType: 'input',
            name: 'skipExpression',
            label: '跳过表达式'
          }
        ]
      }
    }
  },
  watch: {
    'formData.conditionExpression': function(val) {
      if (StrUtil.isNotBlank(val)) {
        if (this.notValues.indexOf(this.forwardConditions.findIndex(item => this.formData.conditionExpression == item.value)) == -1) {
          console.log('this.notValues----------------------------------------', this.notValues)
          val = val.replace('#@#{}', ',' + this.formData.conditionValue)
        } else {
          val = val.replace('#@#{}', '')
        }

        val = val.replace('#@#{myVar}', this.formData.conditionVariable)

        const newCondition = this.modeler.get('moddle').create('bpmn:FormalExpression', { body: val })
        // console.log(newCondition)
        this.updateProperties({ conditionExpression: newCondition })
      }
      // else {
      //   this.updateProperties({ conditionExpression: null })
      // }
    },
    'formData.conditionValue': function(val) {
      var newConditionValue = ''

      if (this.notValues.indexOf(this.forwardConditions.findIndex(item => this.formData.conditionExpression == item.value)) == -1) {
        // val = val.replace('#@#{}', this.formData.conditionValue)
        newConditionValue = this.formData.conditionExpression.replace('#@#{}', ',' + val)
      } else {
        newConditionValue = this.formData.conditionExpression.replace('#@#{}', '')
      }

      newConditionValue = newConditionValue.replace('#@#{myVar}', this.formData.conditionVariable)

      const newCondition = this.modeler.get('moddle').create('bpmn:FormalExpression', { body: newConditionValue })
      this.updateProperties({ conditionExpression: newCondition })
    },
    'formData.conditionVariable': function() {
      var newConditionValue = ''

      if (this.notValues.indexOf(this.forwardConditions.findIndex(item => this.formData.conditionExpression == item.value)) == -1) {
        newConditionValue = this.formData.conditionExpression.replace('#@#{}', ',' + this.formData.conditionValue)
      } else {
        newConditionValue = this.formData.conditionExpression.replace('#@#{}', '')
      }

      newConditionValue = newConditionValue.replace('#@#{myVar}', this.formData.conditionVariable)

      console.log(newConditionValue)

      const newCondition = this.modeler.get('moddle').create('bpmn:FormalExpression', { body: newConditionValue })
      this.updateProperties({ conditionExpression: newCondition })
    },
    'formData.skipExpression': function(val) {
      if (StrUtil.isNotBlank(val)) {
        this.updateProperties({ 'flowable:skipExpression': val })
      } else {
        delete this.element.businessObject.$attrs[`flowable:skipExpression`]
      }
    }
  },
  created() {
    let cache = commonParse(this.element)
    cache = conditionExpressionParse(cache)
    this.formData = cache
    this.computedExecutionListenerLength()
  },
  methods: {
    computedExecutionListenerLength() {
      // this.executionListenerLength = this.element.businessObject.extensionElements?.values
      //   ?.filter(item => item.$type === 'flowable:ExecutionListener').length ?? 0
      if (this.element.businessObject.extensionElements) {
        if (this.element.businessObject.extensionElements.values) {
          this.executionListenerLength = this.element.businessObject.extensionElements.values.filter(item => item.$type === 'flowable:ExecutionListener').length
        } else {
          this.executionListenerLength = 0
        }
      } else {
        this.executionListenerLength = 0
      }
    }
  }
}
</script>

<style></style>
