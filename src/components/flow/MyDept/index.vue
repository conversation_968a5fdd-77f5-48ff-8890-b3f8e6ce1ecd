<!-- 改 -->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="部门名称" prop="roleName">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入部门名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-show="checkType === 'multiple'"
      ref="dataTable"
      row-key="id"
      v-loading="loading"
      :data="deptList"
      @selection-change="handleMultipleDeptSelect"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="部门名称" prop="departName" />
    </el-table>
    <el-table
      v-show="checkType === 'single'"
      v-loading="loading"
      :data="deptList"
      row-key="id"
      @current-change="handleSingleDeptSelect"
    >
      <el-table-column width="55" align="center">
        <template slot-scope="scope">
          <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
          <el-radio v-model="radioSelected" :label="scope.row.id">{{ '' }}</el-radio>
        </template>
      </el-table-column>
      <el-table-column label="部门名称" prop="departName" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[5, 10]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { StrUtil } from '@/utils/StrUtil'
import { postAction } from '@/api/manage'

export default {
  name: 'FlowMyDept',
  // dicts: ['sys_normal_disable'],
  // 接受父组件的值
  props: {
    // 回显数据传值
    selectValues: {
      type: Number | String | Array,
      default: null,
      required: false
    },
    checkType: {
      type: String,
      default: 'multiple',
      required: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      deptList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined
      },
      // 表单参数
      form: {},
      radioSelected: null, // 单选框传值
      selectDeptList: [] // 回显数据传值
    }
  },
  watch: {
    selectValues: {
      handler(newVal) {
        if (StrUtil.isNotBlank(newVal)) {
          if (newVal instanceof Number || newVal instanceof String) {
            this.radioSelected = newVal
          } else {
            this.selectDeptList = newVal
          }
        }
      },
      immediate: true
    },
    deptList: {
      handler(newVal) {
        if (StrUtil.isNotBlank(newVal) && this.selectDeptList.length > 0) {
          this.$nextTick(() => {
            this.$refs.dataTable.clearSelection()
            // this.selectDeptList?.split(',').forEach((key) => {
            //   this.$refs.dataTable.toggleRowSelection(
            //     newVal.find((item) => key == item.roleId),
            //     true
            //   )
            // })
            this.selectDeptList &&
            this.selectDeptList.split(',').forEach((key) => {
              this.$refs.dataTable.toggleRowSelection(
                newVal.find((item) => key == item.id),
                true
              )
            })
          })
        }
      },
      immediate: true, // 立即生效
      deep: true // 监听对象或数组的时候，要用到深度监听
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true
      postAction('/flowable/right/getMyDepart', this.queryParams).then(res => {
        if (res.success) {
          this.deptList = res.result.records
          this.total = res.result.total
        }
        this.loading = false
      })
    },
    // 多选框选中数据
    handleMultipleDeptSelect(selection) {
      console.log(selection)
      const idList = selection.map((item) => item.id)
      const nameList = selection.map((item) => item.departName)
      this.$emit('handleDeptSelect', idList.join(','), nameList.join(','))
    },
    // 单选框选中数据
    handleSingleDeptSelect(selection) {
      this.radioSelected = selection.id
      const name = selection.departName
      this.$emit('handleDeptSelect', this.radioSelected.toString(), name)
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.name = undefined
      this.handleQuery()
    }
  }
}
</script>
<style>
/*隐藏radio展示的label及本身自带的样式*/
/*.el-radio__label{*/
/*  display:none;*/
/*}*/
</style>
