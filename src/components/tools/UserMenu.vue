<template>
  <div class="user-wrapper" :class="theme">
    <a-dropdown>
      <span class="action action-full ant-dropdown-link user-dropdown-menu">
        <a-avatar class="avatar" size="small" :src="require('@/assets/user.png')" />
        <span v-if="isDesktop()" class="user-info">{{ nickname() }}</span>
      </span>
      <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
        <a-menu-item key="5" @click="updateCurrentDepart">
          <a-icon type="cluster" />
          <span>切换部门</span>
        </a-menu-item>
      </a-menu>
    </a-dropdown>
    <UserNotice @showmsgpopup='showmsgpopup' />
    <span class="action" style="position:relative;height:100%;line-height:64px;top:-27px">
      <a class="logout_title" href="javascript:;" @click="handleLogout">
        <a-icon type="logout" />
        <span v-if="isDesktop()">&nbsp;退出登录</span>
      </a>
    </span>
    <user-password ref="userPassword"></user-password>
    <depart-select ref="departSelect" :closable="true" title="部门切换"></depart-select>
    <setting-drawer ref="settingDrawer" :closable="true" title="系统设置"></setting-drawer>
    <noticepopup ref="noticepopup"></noticepopup>

  </div>
</template>

<script>
import Vue from 'vue'
import { mapActions, mapGetters, mapState } from 'vuex'
import { UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types'
import { mixinDevice } from '@/utils/mixin.js'
import { getFileAccessHttpUrl, getAction } from '@/api/manage'
import SettingDrawer from '@/components/setting/SettingDrawer'
import UserNotice from './UserNotice'
import UserPassword from './UserPassword'
import DepartSelect from './DepartSelect'
import noticepopup from './noticepopup'

export default {
  name: 'UserMenu',
  components: {
    SettingDrawer,
    UserNotice,
    UserPassword,
    DepartSelect,
    noticepopup
  },
  mixins: [mixinDevice],
  props: {
    theme: {
      type: String,
      required: false,
      default: 'dark'
    }
  },
  data() {
    return {
      // update-begin author:sunjianlei date:20200219 for: 头部菜单搜索规范命名 --------------
      searchMenuOptions: [],
      searchMenuComp: 'span',
      searchMenuVisible: false
      // update-begin author:sunjianlei date:20200219 for: 头部菜单搜索规范命名 --------------
    }
  },
  computed: {
    ...mapState({
      // 后台菜单
      permissionMenuList: state => state.user.permissionList
    })
  },
  watch: {
    // update-begin author:sunjianlei date:20200219 for: 菜单搜索改为动态组件，在手机端呈现出弹出框
    device: {
      immediate: true,
      handler() {
        this.searchMenuVisible = false
        this.searchMenuComp = this.isMobile() ? 'a-modal' : 'span'
      }
    }
    // update-end author:sunjianlei date:20200219 for: 菜单搜索改为动态组件，在手机端呈现出弹出框
  },
  created() {
    const lists = []
    this.searchMenus(lists, this.permissionMenuList)
    this.searchMenuOptions = [...lists]
  },
  mounted() {
    // 如果是单点登录模式
    if (process.env.VUE_APP_SSO == 'true') {
      const depart = this.userInfo().orgCode
      if (!depart) {
        this.updateCurrentDepart()
      }
    }
  },
  methods: {
    showmsgpopup() {
      this.$refs.noticepopup.show()
    },
    /* update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
    showClick() {
      this.searchMenuVisible = true
    },
    hiddenClick() {
      this.shows = false
    },
    /* update_end author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
    ...mapActions(['Logout']),
    ...mapGetters(['nickname', 'avatar', 'userInfo']),
    getAvatar() {
      return getFileAccessHttpUrl(this.avatar())
    },
    handleLogout() {
      const that = this
      this.$confirmElement('真的要注销登录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        return that.Logout(1).then(() => {
          // update-begin author:wangshuai date:20200601 for: 退出登录跳转登录页面

          // that.$router.push({ path: that.$ls.get('loginSystemPath', '/user/login') })
          // update-end author:wangshuai date:20200601 for: 退出登录跳转登录页面
          window.location.reload()
        }).catch(err => {
          that.$message.error({
            title: '错误',
            description: err.message
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    updatePassword() {
      const username = this.userInfo().username
      this.$refs.userPassword.show(username)
    },
    updateCurrentDepart() {
      this.$refs.departSelect.show()
    },
    systemSetting() {
      this.$refs.settingDrawer.showDrawer()
    },
    /* update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
    searchMenus(arr, menus) {
      for (const i of menus) {
        if (!i.hidden && i.component !== 'layouts/RouteView') {
          arr.push(i)
        }
        if (i.children && i.children.length > 0) {
          this.searchMenus(arr, i.children)
        }
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    // update_begin author:sunjianlei date:20191230 for: 解决外部链接打开失败的问题
    searchMethods(value) {
      const route = this.searchMenuOptions.filter(item => item.id === value)[0]
      if (route.meta.internalOrExternal === true || route.component.includes('layouts/IframePageView')) {
        window.open(route.meta.url, '_blank')
      } else {
        this.$router.push({ path: route.path })
      }
      this.searchMenuVisible = false
    },
    // update_end author:sunjianlei date:20191230 for: 解决外部链接打开失败的问题
    /* update_end author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
    /* update_begin author:liushaoqian date:20200507 for: 刷新缓存*/
    clearCache() {
      getAction('sys/dict/refleshCache').then((res) => {
        if (res.success) {
          // 重新加载缓存
          getAction('sys/dict/queryAllDictItems').then((res) => {
            if (res.success) {
              Vue.ls.remove(UI_CACHE_DB_DICT_DATA)
              Vue.ls.set(UI_CACHE_DB_DICT_DATA, res.result, 7 * 24 * 60 * 60 * 1000)
            }
          })
          this.$message.success('刷新缓存完成！')
        }
      }).catch(e => {
        this.$message.warn('刷新缓存失败！')
        console.log('刷新失败', e)
      })
    }
    /* update_end author:liushaoqian date:20200507 for: 刷新缓存*/
  }
}
</script>

<style lang="less" scoped>
.avatar{
  width: 30px !important;
  height: 30px !important;
  position: relative;
  top: 5px;
}

.user-info{
  font-size: 14px !important;
}
/* update_begin author:zhaoxin date:20191129 for: 让搜索框颜色能随主题颜色变换*/
/* update-begin author:sunjianlei date:20191220 for: 解决全局样式冲突问题 */
.user-wrapper .search-input {
  width: 180px;
  color: inherit;

  /deep/ .ant-select-selection {
    background-color: inherit;
    border: 0;
    border-bottom: 1px solid white;
    &__placeholder,
    &__field__placeholder {
      color: inherit;
    }
  }
}
/* update-end author:sunjianlei date:20191220 for: 解决全局样式冲突问题 */
/* update_end author:zhaoxin date:20191129 for: 让搜索框颜色能随主题颜色变换*/
</style>

<style lang="less">
@topHeight: 60px;

.logout_title {
  color: inherit;
  text-decoration: none;
}

.user-dropdown-menu-wrapper.ant-dropdown-menu {
  padding: 0 !important;
}

.layout .top-nav-header-index .user-wrapper {
  float: right;
  height: @topHeight !important;
  line-height: @topHeight !important;
  color: #000000;
}

.layout .top-nav-header-index .user-wrapper .action {
  height: @topHeight !important;
  line-height: @topHeight !important;
}
</style>
