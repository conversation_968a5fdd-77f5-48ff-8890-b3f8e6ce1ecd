<template>
  <a-dropdown @visibleChange="updateData">
    <span class="action" style="position: relative; height: 100%; line-height: 64px; top: -27px">
      <a class="action-title">
        <a-icon type="bell" />
        <span v-if="isDesktop()">&nbsp;我的消息</span>
      </a>
    </span>
    <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
      <a-menu-item key="1" @click="seeAllMessage(1)">
        <span>待办消息</span>
        <span class="info-number">({{ messageNumber.todoMessageNumber }})</span>
      </a-menu-item>
      <!-- <a-menu-item key="2" @click="seeAllMessage(2)">
        <span>已办消息</span>
        <span class="info-number">({{ messageNumber.doneMessageNumber }})</span>
      </a-menu-item>
      <a-menu-item key="3" @click="seeAllMessage(3)">
        <span>待阅消息</span>
        <span class="info-number">({{ messageNumber.unreadMessageNumber }})</span>
      </a-menu-item>
      <a-menu-item key="4" @click="seeAllMessage(4)">
        <span>已阅消息</span>
        <span class="info-number">({{ messageNumber.readMessageNumber }})</span>
      </a-menu-item> -->
    </a-menu>
  </a-dropdown>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'
import { getAction } from '@/api/manage'
export default {
  name: 'UserNotice',
  components: {
  },
  mixins: [mixinDevice],
  data() {
    return {
      messageNumber: {
        todoMessageNumber: 0,
        doneMessageNumber: 0,
        unreadMessageNumber: 0,
        readMessageNumber: 0
      },
      url: {
        list: '/message/getMessageCount'
      }
    }
  },
  methods: {
    updateData(visible) {
      if (!visible) return
      setTimeout(() => {
        // 延迟300ms，进行数据请求,保证dom动画流畅
        getAction(this.url.list).then((res) => {
          if (res.result) {
            this.messageNumber = {
              todoMessageNumber: res.result.num
              // doneMessageNumber: res.result.done,
              // unreadMessageNumber: res.result.unread,
              // readMessageNumber: res.result.read
            }
          }
        })
      }, 300)
    },
    seeAllMessage(code) {
      this.$emit('showmsgpopup')
      return
      if (code !== 1 && code !== 2 && code !== 3 && code !== 4) return
      let target = ''
      switch (code) {
        case 1:
          target = '/message-management/todoMessage'
          break
        case 2:
          target = '/message/done'
          break
        case 3:
          target = '/message/unread'
          break
        case 4:
          target = '/message/read'
          break
      }
      this.$router.push(target)
    }
  }
}
</script>

<style lang="less" scoped>
.action-title {
  color: inherit;
  text-decoration: none;
}
.info-number {
  padding-left: 24px;
}
</style>
