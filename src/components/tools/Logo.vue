<template>
  <div class="logo">
    <router-link :to="{name:'dashboard'}">

      <!-- update-begin- author:sunjianlei --- date:20190814 --- for: logo颜色根据主题颜色变化 -->
      <div v-if="navTheme === 'dark'" class="logo-white"></div>
      <div v-else class="logo-img"></div>
      <!-- update-begin- author:sunjianlei --- date:20190814 --- for: logo颜色根据主题颜色变化 -->

      <h1 v-if="showTitle">{{ platform }}</h1>
    </router-link>
  </div>
</template>

<script>
import { mixin } from '@/utils/mixin.js'
import { platformName, systemName } from '@/defaultSettings'

export default {
  name: 'Logo',
  mixins: [mixin],
  props: {
    platform: {
      type: String,
      default: platformName,
      required: false
    },
    title: {
      type: String,
      default: systemName,
      required: false
    },
    showTitle: {
      type: <PERSON>olean,
      default: true,
      required: false
    }
  }
}
</script>
<style lang="less" scoped>
  /*缩小首页布 局顶部的高度*/
  @height: 64px;

  .sider {
    box-shadow: none !important;
    .logo {
      height: @height !important;
      line-height: @height !important;
      box-shadow: none !important;
      transition: background 300ms;

      a {
        color: white;
        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }

        &>img {
          height: 26px;
        }

        .logo-white {
          width: 59px;
          height: 26px;
          background: url('~@/assets/logo-white.png') no-repeat center;
          float: left;
          background-size: 34px 31px;
        }
        .logo {
          width: 59px;
          height: 26px;
          background: url('~@/assets/logo.png') no-repeat center;
          float: left;
          background-size: 34px 31px;
        }
      }
    }

    &.light .logo {
      background-color: @primary-color;
    }
  }
</style>
