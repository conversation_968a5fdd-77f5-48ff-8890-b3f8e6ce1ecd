<template>
  <j-modal destroyOnClose switchFullscreen :title="title" :width="width" :visible="visible" @cancel="close">
    <div style="margin-top: 15px;">
      <todomessage @closemsg='closemsg'></todomessage>
    </div>
    <template slot="footer">
      <a-button key="close" @click="close">关闭</a-button>
    </template>
  </j-modal>
</template>

<script>
// import { JeecgListMixin } from '@/mixins/JeecgListMixin'
// import EventListMixin from '@/mixins/EventListMixin'
import todomessage from '../../views/message-management/todoMessage.vue'
export default {
  name: 'MyeventsbanliFormal',
  // mixins: [EventListMixin, JeecgListMixin],
  components: { todomessage },
  data() {
    return {
      title: '待办消息',
      width: 1200,
      visible: false,
      url: {}
    }
  },
  methods: {
    closemsg() {
      this.visible = false
    },
    show() {
      this.visible = true
    },
    close() {
      this.visible = false
    }

  }
}
</script>
<style lang="less" scoped>
::v-deep .ant-modal-body {
  padding: 0 24px 6px 24px;
}
::v-deep .ant-tabs-bar {
  margin: 0 0 8px 0;
}
</style>
