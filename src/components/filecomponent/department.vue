<template>
    <a-modal
    v-model="visible"
    title="涉评部门"
    @ok="handleOk"
    :footer="null"
    :centered="true"
    width='50%'
    :closable='true'
    :destroyOnClose='true'
    >
    <div class="content">
        <div class="top">
            <div class="topL">序号</div>
            <div class="topR">部门名称</div>
        </div>
        <div class="bottom" v-for="(item,index) in departarr" :key="index">
            <div class="bL">{{item.id}}</div>
            <div class="bR">{{item.name}}</div>
        </div>
    </div>
    </a-modal>
</template>

<script>
export default {
  components: { },
  data() {
    return {
      visible: false,
      departarr: []
    }
  },
  mounted() {
  },
  methods: {
    show(a) {
      this.visible = true
      this.departarr = a
      console.log(this.departarr)
    },
    handleOk() {
      this.visible = false
    },
    dilog() {
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
 /deep/  .ant-modal-content{
      height: 60vh;//弹窗的高度在这里修改
      background: #fff;
      overflow: hidden;
      .ant-modal-close{
      opacity: 0;
    }

  }
 /deep/   .ant-modal-header {
      padding: 14px 17px 14px 19px;
      background: url('./images/tk.png') no-repeat center;
      background-size:100% 100% ;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #FFFFFF;
      height: 44px;
      display: flex;
      align-items: center;
      position: relative;
      .ant-modal-title {
      color: #fff;
      font-weight: 500;
      font-size: 16px;
  }
      &::after{
        display: block;
        content: '';
        width: 17px;
        height: 17px;
        background: url('./images/close.png') no-repeat center;
        background-size:100% 100% ;
        position: absolute;
        right: 14px;
        top: 14px;
        cursor: pointer;

      }

  }
 /deep/   .ant-modal-body {
    padding: 0;
    position: relative;
    height: calc(60vh - 44px);
    // border: 3px solid red;
    overflow: auto;

    .content{
        // margin: calc((60vh - 44px - 418px) / 2) auto;
        margin: 30px auto;
        width: 91%;
        overflow: auto;
        border-top: 1px solid #E5E5E5;
        height: calc(60vh - 104px);
        overflow: auto;
        .top{
            width: 100%;
            height: 38px;
            background: #F8FBFF;
            opacity: 0.8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 20px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            line-height: 37px;
            text-align: center;
            .topL{
                width: 50%;
                border: 1px solid #E5E5E5;
                border-right: none;
            }
            .topR{
                width: 50%;
                  border: 1px solid #E5E5E5;
            }
        }
        .bottom{
            width: 100%;
            height: 38px;
            background: #fff;
            opacity: 0.8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 17px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            line-height: 37px;
            text-align: center;
            .bL{
                width: 50%;
                border: 1px solid #E5E5E5;
               border-right: none;

            }
            .bR{
                width: 50%;
                border: 1px solid #E5E5E5;
                // font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #333333;
            }

        }
    }
  }
</style>
