<template>
  <div id="userLayout" :class="['user-layout-wrapper', device]" v-if="showFlag">
    <div class="container">
      <div class="top">
        <!-- <div class="header">
          <a href="/">
            <img src="~@/assets/logo.svg" class="logo" alt="logo">
            <span class="title">{{ systemName }}</span>
          </a>
        </div>
        <div class="desc">
          Jeecg Boot 是中国最具影响力的 企业级 快速开发平台
        </div> -->
        <img src="~@/assets/login/new-sjzx.png" class="header-img" :alt="systemName">
      </div>

      <div class="login-container">
        <div class="login-box">
          <div class="login-main">
            <route-view></route-view>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="links">
          <!-- <a href="http://doc.jeecg.com" target="_blank">帮助</a>
          <a href="https://github.com/zhangdaiscott/jeecg-boot" target="_blank">隐私</a>
          <a href="https://github.com/zhangdaiscott/jeecg-boot/blob/master/LICENSE" target="_blank">条款</a> -->
        </div>
        <div class="copyright">
          <!-- Copyright &copy; 2019 <a href="http://www.jeecg.com" target="_blank">JEECG开源社区</a> 出品 -->
          <!-- 登录页面底部文字 -->
          <!-- Copyright &copy; {{ year }} {{ footText }} -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RouteView from '@/components/layouts/RouteView'
import { mixinDevice } from '@/utils/mixin.js'
import config from '@/defaultSettings'

export default {
  name: 'UserLayout',
  components: { RouteView },
  mixins: [mixinDevice],
  data() {
    return {
      systemName: config.systemName,
      footText: config.footText,
      year: new Date().getFullYear(),
      showFlag: process.env.VUE_APP_SSO != 'true'
    }
  },
  mounted() {
    document.body.classList.add('userLayout')

    this.$ls.set('loginSystemPath', this.$route.fullPath)
  },
  beforeDestroy() {
    document.body.classList.remove('userLayout')
  }
}
</script>

<style lang="less" scoped>
  #userLayout.user-layout-wrapper {
    position: absolute;
    width: 100%;
    height: 100%;
    min-width: 1200px;
    min-height: 600px;
    background: #07112c url('~@/assets/login/linyishijiancenterbg.jpg') no-repeat;
    background-size: cover;
    background-position: center center;

    &.mobile {
      .container {
        .main {
          max-width: 368px;
          width: 98%;
        }
      }
    }

    .container {
      width: 100%;
      min-height: 100%;
      // background: #f0f2f5 url(~@/assets/background.svg) no-repeat 50%;
      background-size: 100%;
      // padding: 110px 0 144px;
      position: relative;

      a {
        text-decoration: none;
      }

      .top {
        text-align: center;

        .header {
          height: 44px;
          line-height: 44px;
          z-index: 99999;
          .badge {
            position: absolute;
            display: inline-block;
            line-height: 1;
            vertical-align: middle;
            margin-left: -12px;
            margin-top: -10px;
            opacity: 0.8;
          }

          .logo {
            height: 44px;
            vertical-align: top;
            margin-right: 16px;
            border-style: none;
          }

          .title {
            font-size: 33px;
            color: rgba(0, 0, 0, .85);
            font-family: "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            font-weight: 600;
            position: relative;
            top: 2px;
          }
        }
        .desc {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          margin-top: 12px;
          margin-bottom: 40px;
        }

        .header-img {
          min-width: 420px;
          width: 30vw;
          margin-top: 5vh;

          @media (max-height: 700px) {
            & {
              margin-top: 2vh;
            }
          }
        }
      }

      .login-container {
        position: absolute;
        top: 0;
        min-width: 400px;
        width: 25%;
        height: 100%;
        right: 16vw;
        display: flex;
        align-items:center;
        justify-content:center;

        // @media (max-width: 1400px) {
        //   & {
        //     right: 16vw;
        //   }
        // }

        .login-box {
            width: 100%;
            // min-height: 500px;
            // margin-top: -5vh;

          .login-main {
            overflow: hidden;
            // margin-top: 20px;
            background-color: rgba(55, 138, 202, .16);
            border-radius: 4px;
            padding: 10px 40px 0 40px;
          }
        }
      }

      .main {
        // min-width: 260px;
        // width: 368px;
        // margin: 0 auto;
      }

      .footer {
        position: absolute;
        width: 100%;
        bottom: 0;
        padding: 0 16px;
        margin: 48px 0 24px;
        text-align: center;

        .links {
          margin-bottom: 8px;
          font-size: 14px;
          a {
            color: rgba(0, 0, 0, 0.45);
            transition: all 0.3s;
            &:not(:last-child) {
              margin-right: 40px;
            }
          }
        }
        .copyright {
          color: #ffffff;
          font-size: 14px;
        }
      }
    }
  }
</style>
