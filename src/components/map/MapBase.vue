<template>
  <div class="map-base-container" :style="{width: mapWidth, height: mapHeight}">
    <div class="map-container" :id="mapIDName"></div>
    <slot name="left"></slot>
    <slot name="right"></slot>
  </div>
</template>

<script>
import { getCurrentNetworkSituation } from '@/utils/util'
import { baseMapUrlInGovernmentExtranet } from '@/defaultSettings'
import LYGeo<PERSON>son from '@/static/map/linyi.json'

export default {
  name: 'MapBase',
  props: {
    mapWidth: {
      type: String,
      required: false,
      default: '100%'
    },
    mapHeight: {
      type: String,
      required: false,
      default: '100%'
    },
    // 是否自动初始化
    autoInit: {
      type: Boolean,
      required: false,
      default: true
    },
    // 是否显示渝中区域
    showArea: {
      type: Boolean,
      required: false,
      default: true
    },
    // 初始化层级
    initZoom: {
      type: Number,
      required: false,
      default: 13
    },
    // 默认中心点坐标
    mapCenter: {
      type: Array,
      required: false,
      default() {
        return [29.95, 113.93]
        // return [35.109, 118.363]
      }
    }
  },
  data() {
    return {
      mapIDName: '',
      map: null
    }
  },
  created() {
    this.mapIDName = this.createUUID(6)
  },
  mounted() {
    if (this.autoInit) this.initMap()
  },
  methods: {
    createUUID(len, radix) {
      const chars = '0123456789'.split('')
      const uuid = []
      radix = radix || chars.length
      if (len) {
        for (let i = 0; i < len; i++) {
          uuid[i] = chars[0 | Math.random() * radix]
        }
      }
      return 'map-' + uuid.join('')
    },
    initMap() {
      this.map = L.map(this.mapIDName, {
        center: this.mapCenter, // 地图中心
        zoom: this.initZoom, // 缩放比列
        minZoom: 7,
        maxZoom: 18,
        zoomControl: false, // 禁用 + - 按钮
        crs: L.supermap.CRS.TianDiTu_WGS84,
        doubleClickZoom: false, // 禁用双击放大
        attributionControl: false // 移除右下角leaflet标识
      })
      // 根据配置的部署环境，进行对应网络环境地图底图加载
      if (getCurrentNetworkSituation() === 1) {
        // 政务外网环境
        L.tileLayer(`${baseMapUrlInGovernmentExtranet || window.location.origin}/DataServer?T=vec_c&x={x}&y={y}&l={z}`, { maxZoom: 18, minZoom: 7, zoomOffset: 1 }).addTo(this.map)
        L.tileLayer(`${baseMapUrlInGovernmentExtranet || window.location.origin}/DataServer?T=cva_c&x={x}&y={y}&l={z}`, { maxZoom: 18, minZoom: 7, zoomOffset: 1 }).addTo(this.map)
      } else {
        // 互联网环境
        new L.supermap.TiandituTileLayer({ key: '1d109683f4d84198e37a38c442d68311' }).addTo(this.map)
        new L.supermap.TiandituTileLayer({
          isLabel: true,
          key: '1d109683f4d84198e37a38c442d68311'
        }).addTo(this.map)
      }
      if (this.showArea) {
        L.geoJSON(LYGeoJson, {
          style: function(feature) {
            return {
              fillOpacity: 0.4,
              fillColor: '#80d8ff',
              strokeColor: '#0091ea'
            }
          },
          filter: function(feature) {
            return true
          }
        }).addTo(this.map)
      }

      // 地图绑定事件
      this.$emit('load')
    }
  }
}
</script>

<style lang="less" scoped>
  .map-base-container {
    position: relative;
    .map-container {
        width: 100%;
        height: 100%;
    }
  }
</style>
