<template>
  <div class="footer">
    <div class="links">
      <!-- <a href="http://www.jeecg.com" target="_blank">JEECG 首页</a>
      <a href="https://github.com/zhangdaiscott/jeecg-boot" target="_blank">
        <a-icon type="github"/>
      </a>
      <a href="https://ant.design/">Ant Design</a>
      <a href="https://vuecomponent.github.io/ant-design-vue/docs/vue/introduce-cn/">Vue Antd</a> -->
    </div>
    <div class="copyright">
      <!-- 全局页面底部文字 -->
      <!-- Copyright &copy;{{ year }} {{ footText }} -->
    </div>
  </div>
</template>

<script>
import config from '@/defaultSettings'
export default {
  name: 'LayoutFooter',
  data() {
    return {
      footText: config.footText,
      year: new Date().getFullYear()
    }
  }
}
</script>

<style lang="less" scoped>
  .footer {
    padding: 5px 0;
    text-align: center;
    background: #F5FAFF;

    .links {
      // margin-bottom: 8px;

      a {
        color: rgba(0, 0, 0, .45);

        &:hover {
          color: rgba(0, 0, 0, .65);
        }

        &:not(:last-child) {
          margin-right: 40px;
        }
      }
    }
    .copyright {
      color: #999999;
      font-size: 16px;
    }
  }
</style>
