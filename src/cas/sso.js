import Vue from 'vue'
import {
  ACCESS_TOKEN
} from '@/store/mutation-types'
import store from '@/store'
/**
 * 单点登录
 */
const init = (callback) => {
  if (process.env.VUE_APP_SSO == 'true') {
    const token = Vue.ls.get(ACCESS_TOKEN)
    const st = getUrlParam('ticket')
    const sevice = 'http://' + window.location.host + '/'
    if (token) {
      loginSuccess(callback)
    } else {
      if (st) {
        validateSt(st, sevice, callback)
      } else {
        const serviceUrl = encodeURIComponent(sevice)
        window.location.href = window._CONFIG['casPrefixUrl'] + '/login?service=' + serviceUrl
      }
    }
  } else {
    callback && callback()
  }
}
const SSO = {
  init: init
}

function getUrlParam(paraName) {
  const url = document.location.toString()
  const arrObj = url.split('?')

  if (arrObj.length > 1) {
    const arrPara = arrObj[1].split('&')
    let arr

    for (let i = 0; i < arrPara.length; i++) {
      arr = arrPara[i].split('=')

      if (arr != null && arr[0] == paraName) {
        return arr[1]
      }
    }
    return ''
  } else {
    return ''
  }
}

function validateSt(ticket, service, callback) {
  const params = {
    ticket: ticket,
    service: service
  }
  store.dispatch('ValidateLogin', params).then(res => {
    // this.departConfirm(res)
    if (res.success) {
      loginSuccess(callback)
    } else {
      // if (res.code === 500 || res.code === 5001) {
      //   window.sessionStorage.setItem('noFlag', '1')
      //   callback()
      // } else {
      const sevice = 'http://' + window.location.host + '/'
      const serviceUrl = encodeURIComponent(sevice)
      window.location.href = window._CONFIG['casPrefixUrl'] + '/login?service=' + serviceUrl
      // }
    }
  }).catch((err) => {
    console.log(err)
  })
}

function loginSuccess(callback) {
  callback()
}
export default SSO
