<?xml version="1.0" encoding="UTF-8"?>
<svg width="70px" height="43px" viewBox="0 0 70 43" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>画板</title>
    <defs>
        <linearGradient x1="94.918724%" y1="3.67422027%" x2="8.41770063%" y2="95.5558801%" id="linearGradient-1">
            <stop stop-color="#1D7AFF" offset="0%"></stop>
            <stop stop-color="#02A9FF" offset="44.9737762%"></stop>
            <stop stop-color="#1A4FCB" offset="100%"></stop>
        </linearGradient>
        <path d="M8.6673453,1.0115233 C4.46858879,-1.50211232 0.206249009,1.04273628 0.00757756743,4.55421768 C-0.0669242242,5.87102322 0.40738276,7.32438633 1.45533244,8.47537193 C4.91977499,12.1638906 9.6571709,9.09183224 13.4359983,13.1256467 C13.6292106,7.74332604 11.8981131,2.92820705 8.6673453,1.0115233 Z" id="path-2"></path>
        <filter x="-52.0%" y="-38.1%" width="204.1%" height="206.7%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0197664295   0 0 0 0 0.301017521   0 0 0 0 0.84131567  0 0 0 0.660347465 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="65.4418896%" y1="55.4856949%" x2="80.5164323%" y2="74.5006497%" id="linearGradient-4">
            <stop stop-color="#F2F9FF" stop-opacity="0.319957386" offset="0%"></stop>
            <stop stop-color="#CCE6FF" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <path d="M28.721439,0 C31.2099423,-4.57130628e-16 33.2272727,2.01733046 33.2272727,4.50583369 L33.2272727,12.0557168 C33.2272727,12.2718053 33.2120615,12.484341 33.1826514,12.6923116 L33.2270472,12.6927095 L33.2272727,12.6807095 C33.2272727,20.7898661 26.5700822,27.3636364 18.3580215,27.3636364 C14.2926884,27.3636364 10.6084053,25.7526105 7.92418535,23.141815 C7.56064525,22.8483655 7.31815338,22.6245405 7.31815338,22.6245405 C7.31815338,22.6245405 6.19347278,21.9210583 5.14094066,22.2537012 C3.3182265,23.3058811 2.35437007,23.2950991 1.55539147,23.0956605 C1.02273907,22.9627014 0.50427525,22.6773515 3.69537734e-13,22.2396106 C1.23245971,22.1302098 2.04601021,21.9950537 2.44065153,21.8341422 C3.14979345,21.5449958 3.41551849,20.922731 3.71868848,20.6601519 C4.27493207,20.1783827 4.88417644,19.9309499 5.54642158,19.9178535 C5.44596753,19.8755805 5.25612469,19.6426094 4.97689306,19.2189402 C4.60748653,18.4146025 5.00108365,16.893792 4.94963359,15.9420035 C4.89818354,14.990215 3.87927372,13.5316147 3.87927372,13.5316147 C3.87927372,13.5316147 5.40126427,13.7475598 6.08543707,14.148753 C6.76960988,14.5499461 8.09532052,15.6099634 8.2634551,18.7203305 C8.32775178,19.9097721 8.48580202,20.8372979 8.67969202,21.55007 C10.2016366,22.4178429 11.9682038,22.9142646 13.8521878,22.9142646 C19.5716716,22.9142646 24.209,18.3390656 24.2155983,12.6927759 L24.2156053,4.50583369 C24.2156053,2.01733046 26.2329358,4.57130628e-16 28.721439,0 Z" id="path-6"></path>
        <filter x="-21.1%" y="-18.3%" width="142.1%" height="151.2%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0197664295   0 0 0 0 0.301017521   0 0 0 0 0.84131567  0 0 0 0.660347465 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="69.6121705%" y1="52.8328396%" x2="94.4822233%" y2="73.8030653%" id="linearGradient-9">
            <stop stop-color="#F2F9FF" stop-opacity="0.319957386" offset="0%"></stop>
            <stop stop-color="#CCE6FF" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-10">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="画板" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-53备份-33" transform="translate(13.000000, 0.000000)">
            <rect id="矩形备份-18" fill="url(#linearGradient-1)" x="0" y="0" width="43" height="43" rx="16"></rect>
            <g id="编组" transform="translate(4.886364, 7.818182)">
                <g id="编组-77" transform="translate(6.054373, 2.268555)">
                    <g id="路径备份-12">
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                        <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-2"></use>
                    </g>
                    <g id="编组-76" fill="url(#linearGradient-4)">
                        <ellipse id="椭圆形" filter="url(#filter-5)" cx="2.81614606" cy="1.890983" rx="15.8830638" ry="15.6840355"></ellipse>
                    </g>
                </g>
                <g id="椭圆形">
                    <mask id="mask-7" fill="white">
                        <use xlink:href="#path-6"></use>
                    </mask>
                    <g id="蒙版">
                        <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-6"></use>
                        <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-6"></use>
                    </g>
                    <ellipse fill="url(#linearGradient-9)" filter="url(#filter-10)" mask="url(#mask-7)" cx="17.6853972" cy="5.89541759" rx="15.8830638" ry="15.6840355"></ellipse>
                </g>
            </g>
        </g>
    </g>
</svg>